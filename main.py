from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from routes import dba_route, conversion_route, conversion_route_stage2, performance_route
from common.auth_validate import JWTMiddleware, setup_swagger_auth
import uvicorn

# Create HTTPBearer security scheme for Swagger UI
security = HTTPBearer()

app = FastAPI(
    title="QMigrator Web Agents",
    description="AI API's for QMigrator",
    docs_url="/ai/docs",
    redoc_url="/ai/redoc",
    openapi_url="/ai/openapi.json"
)

API_PREFIX_URL = "ai"

# Setup Swagger UI authentication
# setup_swagger_auth(app)

# Add JWT Authentication Middleware
# app.add_middleware(JWTMiddleware)

# Add CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "*",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"]
)

app.include_router(dba_route.router, prefix=f"/{API_PREFIX_URL}/dba", tags=["DBA"])
app.include_router(performance_route.router, prefix=f"/{API_PREFIX_URL}/performance", tags=["Performance Tuning"])
app.include_router(conversion_route.router, prefix=f"/{API_PREFIX_URL}/conversion", tags=["Migration"])
app.include_router(conversion_route_stage2.router, prefix=f"/{API_PREFIX_URL}/conversion_stage2", tags=["Migration Stage 2"])


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)